import React, { useState, useRef, useDeferredValue } from "react";
import {
  View,
  TextInput,
  Pressable,
  Text,
  StyleProp,
  ViewStyle,
  ScrollView,
} from "react-native";
import { Ionicons } from "@expo/vector-icons";
import { useTranslation } from "react-i18next";
import { cn } from "@/lib/utils";
import globalStyles from "@/lib/globalStyles";
import { useDebounce } from "@/hooks/useDebounce";
import Dropdown, { DropdownItem } from "@/features/shared/components/Dropdown";
import {
  ExperienceFilterStore,
  ExperienceFilterType,
  searchFieldOptions,
} from "../useExperienceFilterStore";

interface SearchInputWithDropdownProps {
  searchField: ExperienceFilterType;
  onSearchFieldChange: (field: ExperienceFilterType) => void;
  onSearch: (field: ExperienceFilterType) => void;
  placeholder?: string;
  style?: StyleProp<ViewStyle>;
  getFilterResultList: ExperienceFilterStore["getFilterResultList"];
}

const SearchInputWithDropdown: React.FC<SearchInputWithDropdownProps> = ({
  searchField,
  onSearchFieldChange,
  onSearch,
  placeholder,
  style,
  getFilterResultList,
}) => {
  const { t } = useTranslation();
  const [isFieldDropdownVisible, setIsFieldDropdownVisible] = useState(false);
  const [isResultsDropdownVisible, setIsResultsDropdownVisible] =
    useState(false);
  const fieldDropdownRef = useRef<View>(null);
  const resultsDropdownRef = useRef<View>(null);
  const textInputRef = useRef<TextInput>(null);

  const [resultsDropdownItems, setResultsDropdownItems] = useState(
    getFilterResultList({ key: searchField.key })?.map((result, index) => ({
      id: index.toString(),
      label: result.value,
      value: result.key,
    }))
  );

  const deferredResultsDropdownItems = useDeferredValue(resultsDropdownItems);

  const currentFieldLabel = t(searchField.labelKey);

  const getPlaceholder = () => {
    if (placeholder) return placeholder;
    return t("common.search") + " " + currentFieldLabel.toLowerCase();
  };

  const debouncedHandleFieldTextChange = useDebounce((text: string) => {
    onSearch({ ...searchField, value: text });
  }, 1000);

  const handleFieldSelectChange = (text: string) => {
    setResultsDropdownItems(
      getFilterResultList({ key: searchField.key, text })?.map(
        (result, index) => ({
          id: index.toString(),
          label: result.value,
          value: result.key,
        })
      )
    );
    setIsResultsDropdownVisible(text ? true : false);
  };

  const handleFieldSelection = (item: DropdownItem<ExperienceFilterType>) => {
    const field = item.value;
    onSearchFieldChange(field);
    setIsFieldDropdownVisible(false);

    setIsResultsDropdownVisible(false);
    textInputRef.current?.clear();

    // Focus text input
    setTimeout(() => {
      textInputRef.current?.focus();
    }, 100);
  };

  // Handle result selection
  const handleResultSelection = (item: DropdownItem<string>) => {
    onSearch({ ...searchField, value: item.value });
    setIsResultsDropdownVisible(false);
    textInputRef.current?.blur();
  };

  const fieldDropdownItems: DropdownItem<ExperienceFilterType>[] =
    searchFieldOptions.map((option) => ({
      id: option.key,
      label: t(option.labelKey),
      value: option,
    }));

  const handleTextInputFocus = () => {
    if (searchField.value) {
      setIsResultsDropdownVisible(true);
    }
  };

  return (
    <View style={[{ flex: 1 }, style]}>
      <View
        className={cn(
          "flex-row items-center pr-5 py-1 pl-3 gap-2.5 bg-light-primary rounded-full"
        )}
      >
        <Pressable
          ref={fieldDropdownRef}
          onPress={() => setIsFieldDropdownVisible(true)}
          className={cn(
            "flex-row items-center px-3 gap-1 py-1 bg-primary-1 rounded-full"
          )}
        >
          <Text className="text-white text-[12px] font-medium">
            {currentFieldLabel}
          </Text>
          <Ionicons name="caret-down" className="text-white text-base" />
        </Pressable>
        {/* Text Input */}
        <View
          ref={resultsDropdownRef}
          className="flex-1 items-center flex-row z-10"
        >
          <TextInput
            ref={textInputRef}
            defaultValue={searchField.value}
            onChangeText={(text) =>
              searchField.type === "text"
                ? debouncedHandleFieldTextChange(text)
                : handleFieldSelectChange(text)
            }
            onFocus={handleTextInputFocus}
            placeholder={getPlaceholder()}
            placeholderTextColor={globalStyles.colors.tertiary2}
            textAlignVertical="center"
            className={cn(
              "text-base h-11 leading-[0] flex-1 text-primary-1 items-center bg-light-primary"
            )}
          />
          {deferredResultsDropdownItems && isResultsDropdownVisible && (
            <ScrollView
              onStartShouldSetResponder={() => true}
              className="bg-white absolute w-[120%] overflow-hidden shadow-md py-2 top-[110%] px-4 rounded-md z-10 max-h-[300px]"
            >
              {deferredResultsDropdownItems.map((item) => (
                <Pressable
                  key={item.id}
                  className={cn(
                    "flex-row overflow-hidden items-center px-5 gap-2.5 py-2.5 min-h-6"
                  )}
                  onPress={() => {
                    handleResultSelection(item);
                  }}
                  android_ripple={{
                    color: globalStyles.colors.light.primary,
                    borderless: false,
                    foreground: true,
                  }}
                >
                  <Text className="flex-row text-base items-center gap-2.5 py-2.5">
                    {item.label}
                  </Text>
                </Pressable>
              ))}
            </ScrollView>
          )}
        </View>
        {searchField.key === "province" && (
          <Pressable
            className="px-2"
            onPress={() => {
              // Could open a province selector modal here
              setIsResultsDropdownVisible(!isResultsDropdownVisible);
            }}
          >
            <Ionicons
              name="location-outline"
              className="text-primary-1 text-xl"
            />
          </Pressable>
        )}
      </View>

      <Dropdown
        items={fieldDropdownItems}
        onSelectItem={handleFieldSelection}
        visible={isFieldDropdownVisible}
        onClose={() => setIsFieldDropdownVisible(false)}
        triggerRef={fieldDropdownRef}
        alignment="left"
      />
    </View>
  );
};

export default SearchInputWithDropdown;
