import { cn } from "@/lib/utils";
import { Ref } from "react";
import { TextInput, TextInputProps, View } from "react-native";

type Props = TextInputProps & {
  required?: boolean;
  className?: string;
  inputClassName?: string;
  ref?: Ref<TextInput>;
};

const InputField = ({ style, className, inputClassName, ...props }: Props) => {
  return (
    <View
      className={cn(
        "px-5 relative bg-light-primary rounded-xs flex-1 flex-row",
        className
      )}
    >
      {props.required && (
        <View className="absolute top-0 rounded-full left-0 size-2 text-lg bg-primary-2 z-10" />
      )}
      <TextInput
        {...props}
        textAlignVertical="center"
        className={cn(
          "h-12 leading-[1] text-primary-1 flex-1 items-center text-base",
          inputClassName
        )}
      />
    </View>
  );
};

export default InputField;
