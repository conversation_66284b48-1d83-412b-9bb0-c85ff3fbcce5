import { Ionicons } from "@expo/vector-icons";
import React, { useState, useRef } from "react";
import {
  StyleProp,
  Text,
  TextStyle,
  View,
  ViewStyle,
  StyleSheet,
  Pressable,
} from "react-native";
import globalStyles from "@/lib/globalStyles";
import { screenWidth } from "@/lib/device";
import Dropdown, { DropdownItem } from "./Dropdown";
import { useTranslation } from "react-i18next";
import { cn } from "@/lib/utils";

type ItemType<Type> = Type extends {
  name?: string;
  value?: string;
}
  ? Type
  : never;

type Props<Type> = {
  items: ItemType<Type>[];
  selected?: ItemType<Type>;
  onSelectItem?: (item: ItemType<Type>) => void;
  label?: string;
  withSearch?: boolean;
  defaultValue?: string;
  required?: boolean;
  alignment?: "left" | "right" | "center";
  textClassName?: string;
  className?: string;
  dropdownClassName?: string;
};

const InputSelect = <Type,>({
  items,
  selected,
  label,
  onSelectItem,
  withSearch = false,
  defaultValue,
  dropdownClassName,
  required = false,
  alignment = "left",
  textClassName,
  className,
}: Props<Type>) => {
  const { t } = useTranslation();
  const [isDropdownVisible, setIsDropdownVisible] = useState(false);
  const triggerRef = useRef<View>(null);

  const renderLabel = () => {
    if (selected || isDropdownVisible) {
      return <Text style={[styles.label]}>{label}</Text>;
    }
    return null;
  };

  // Convert items to DropdownItem format
  const dropdownItems: DropdownItem<string | undefined>[] = items.map(
    (item, index) => ({
      id: item.value || index.toString(),
      label: item.name || "",
      value: item.value,
    })
  );

  const handleSelectItem = (item: DropdownItem<string | undefined>) => {
    // Find the original item to maintain type compatibility
    const originalItem = items.find((i) => i.value === item.value);
    if (originalItem) {
      onSelectItem?.(originalItem);
    }
    setIsDropdownVisible(false);
  };

  const getDisplayText = () => {
    if (selected?.name) return selected.name;
    if (defaultValue) {
      const defaultItem = items.find((item) => item.value === defaultValue);
      return defaultItem?.name || defaultValue;
    }
    return label;
  };

  return (
    <>
      <View
        className={cn("relative flex-row flex-shrink items-center", className)}
      >
        {required && (
          <View className="absolute top-0 left-0 size-2 rounded-full bg-primary-2 z-10" />
        )}
        {renderLabel()}
        <Pressable
          ref={triggerRef}
          onPress={() => setIsDropdownVisible(true)}
          android_ripple={{
            color: globalStyles.rgba().light.primary,
            borderless: false,
            foreground: true,
          }}
          className={cn(
            "flex-row px-5 overflow-hidden py-2.5 rounded-xs h-12 items-center justify-between flex-1 min-w-[100px] gap-2.5",
            dropdownClassName
          )}
        >
          <Text
            className={cn(
              "text-base text-light-secondary flex-1 w-[80%]",
              textClassName
            )}
            numberOfLines={1}
            ellipsizeMode="tail"
          >
            {getDisplayText()}
          </Text>
          <Ionicons
            name="caret-down-sharp"
            className="text-primary-1 text-base"
          />
        </Pressable>
      </View>

      <Dropdown
        items={dropdownItems}
        onSelectItem={handleSelectItem}
        visible={isDropdownVisible}
        onClose={() => setIsDropdownVisible(false)}
        triggerRef={triggerRef}
        alignment={alignment}
        searchable={withSearch}
        searchPlaceholder={t("common.search")}
        itemTextStyle={{
          color: globalStyles.colors.dark.secondary,
        }}
        overlayClassName="bg-black/0"
      />
    </>
  );
};

export default InputSelect;

const styles = StyleSheet.create({
  label: {
    position: "absolute",
    top: -18,
    fontSize: globalStyles.size.sm,
    color: globalStyles.colors.tertiary2,
    paddingLeft: globalStyles.size.xs,
  },
});
